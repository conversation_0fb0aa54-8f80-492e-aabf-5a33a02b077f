{"$schema": "https://biomejs.dev/schemas/2.0.0-beta.6/schema.json", "assist": {"actions": {"source": {"organizeImports": "on", "useSortedAttributes": "on", "useSortedKeys": "on", "useSortedProperties": "on"}}, "enabled": true}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100}, "javascript": {"formatter": {"jsxQuoteStyle": "single", "quoteStyle": "single", "semicolons": "asNeeded"}}, "linter": {"enabled": true, "rules": {"recommended": true}}, "vcs": {"clientKind": "git", "enabled": false, "useIgnoreFile": false}}