[{"header": "Cover page", "id": 1, "limit": "5", "reviewer": "<PERSON>", "status": "In Process", "target": "18", "type": "Cover page"}, {"header": "Table of contents", "id": 2, "limit": "24", "reviewer": "<PERSON>", "status": "Done", "target": "29", "type": "Table of contents"}, {"header": "Executive summary", "id": 3, "limit": "13", "reviewer": "<PERSON>", "status": "Done", "target": "10", "type": "Narrative"}, {"header": "Technical approach", "id": 4, "limit": "23", "reviewer": "<PERSON><PERSON>", "status": "Done", "target": "27", "type": "Narrative"}, {"header": "Design", "id": 5, "limit": "16", "reviewer": "<PERSON><PERSON>", "status": "In Process", "target": "2", "type": "Narrative"}, {"header": "Capabilities", "id": 6, "limit": "8", "reviewer": "<PERSON><PERSON>", "status": "In Process", "target": "20", "type": "Narrative"}, {"header": "Integration with existing systems", "id": 7, "limit": "21", "reviewer": "<PERSON><PERSON>", "status": "In Process", "target": "19", "type": "Narrative"}, {"header": "Innovation and Advantages", "id": 8, "limit": "26", "reviewer": "Assign reviewer", "status": "Done", "target": "25", "type": "Narrative"}, {"header": "Overview of EMR's Innovative Solutions", "id": 9, "limit": "23", "reviewer": "Assign reviewer", "status": "Done", "target": "7", "type": "Technical content"}, {"header": "Advanced Algorithms and Machine Learning", "id": 10, "limit": "28", "reviewer": "Assign reviewer", "status": "Done", "target": "30", "type": "Narrative"}, {"header": "Adaptive Communication Protocols", "id": 11, "limit": "31", "reviewer": "Assign reviewer", "status": "Done", "target": "9", "type": "Narrative"}, {"header": "Advantages Over Current Technologies", "id": 12, "limit": "0", "reviewer": "Assign reviewer", "status": "Done", "target": "12", "type": "Narrative"}, {"header": "Past Performance", "id": 13, "limit": "33", "reviewer": "Assign reviewer", "status": "Done", "target": "22", "type": "Narrative"}, {"header": "Customer Feedback and Satisfaction Levels", "id": 14, "limit": "34", "reviewer": "Assign reviewer", "status": "Done", "target": "15", "type": "Narrative"}, {"header": "Implementation Challenges and Solutions", "id": 15, "limit": "35", "reviewer": "Assign reviewer", "status": "Done", "target": "3", "type": "Narrative"}, {"header": "Security Measures and Data Protection Policies", "id": 16, "limit": "36", "reviewer": "Assign reviewer", "status": "In Process", "target": "6", "type": "Narrative"}, {"header": "Scalability and Future Proofing", "id": 17, "limit": "37", "reviewer": "Assign reviewer", "status": "Done", "target": "4", "type": "Narrative"}, {"header": "Cost-Benefit Analysis", "id": 18, "limit": "38", "reviewer": "Assign reviewer", "status": "Done", "target": "14", "type": "Plain language"}, {"header": "User Training and Onboarding Experience", "id": 19, "limit": "39", "reviewer": "Assign reviewer", "status": "Done", "target": "17", "type": "Narrative"}, {"header": "Future Development Roadmap", "id": 20, "limit": "40", "reviewer": "Assign reviewer", "status": "Done", "target": "11", "type": "Narrative"}, {"header": "System Architecture Overview", "id": 21, "limit": "18", "reviewer": "<PERSON>", "status": "In Process", "target": "24", "type": "Technical content"}, {"header": "Risk Management Plan", "id": 22, "limit": "22", "reviewer": "<PERSON>", "status": "Done", "target": "15", "type": "Narrative"}, {"header": "Compliance Documentation", "id": 23, "limit": "27", "reviewer": "<PERSON>", "status": "In Process", "target": "31", "type": "Legal"}, {"header": "API Documentation", "id": 24, "limit": "12", "reviewer": "<PERSON>", "status": "Done", "target": "8", "type": "Technical content"}, {"header": "User Interface <PERSON>", "id": 25, "limit": "25", "reviewer": "<PERSON><PERSON>", "status": "In Process", "target": "19", "type": "Visual"}, {"header": "Database Schema", "id": 26, "limit": "20", "reviewer": "<PERSON>", "status": "Done", "target": "22", "type": "Technical content"}, {"header": "Testing Methodology", "id": 27, "limit": "14", "reviewer": "Assign reviewer", "status": "In Process", "target": "17", "type": "Technical content"}, {"header": "Deployment Strategy", "id": 28, "limit": "30", "reviewer": "<PERSON>", "status": "Done", "target": "26", "type": "Narrative"}, {"header": "Budget Breakdown", "id": 29, "limit": "16", "reviewer": "<PERSON><PERSON>", "status": "In Process", "target": "13", "type": "Financial"}, {"header": "Market Analysis", "id": 30, "limit": "32", "reviewer": "<PERSON>", "status": "Done", "target": "29", "type": "Research"}, {"header": "Competitor Comp<PERSON>on", "id": 31, "limit": "19", "reviewer": "Assign reviewer", "status": "In Process", "target": "21", "type": "Research"}, {"header": "Maintenance Plan", "id": 32, "limit": "23", "reviewer": "<PERSON>", "status": "Done", "target": "16", "type": "Technical content"}, {"header": "User Personas", "id": 33, "limit": "24", "reviewer": "<PERSON>", "status": "In Process", "target": "27", "type": "Research"}, {"header": "Accessibility Compliance", "id": 34, "limit": "21", "reviewer": "Assign reviewer", "status": "Done", "target": "18", "type": "Legal"}, {"header": "Performance Metrics", "id": 35, "limit": "26", "reviewer": "<PERSON>", "status": "In Process", "target": "23", "type": "Technical content"}, {"header": "Disaster Recovery Plan", "id": 36, "limit": "17", "reviewer": "<PERSON><PERSON>", "status": "Done", "target": "14", "type": "Technical content"}, {"header": "Third-party Integrations", "id": 37, "limit": "28", "reviewer": "<PERSON>", "status": "In Process", "target": "25", "type": "Technical content"}, {"header": "User <PERSON><PERSON><PERSON> Summary", "id": 38, "limit": "15", "reviewer": "Assign reviewer", "status": "Done", "target": "20", "type": "Research"}, {"header": "Localization Strategy", "id": 39, "limit": "19", "reviewer": "<PERSON>", "status": "In Process", "target": "12", "type": "Narrative"}, {"header": "Mobile Compatibility", "id": 40, "limit": "31", "reviewer": "<PERSON>", "status": "Done", "target": "28", "type": "Technical content"}, {"header": "Data Migration Plan", "id": 41, "limit": "22", "reviewer": "Assign reviewer", "status": "In Process", "target": "19", "type": "Technical content"}, {"header": "Quality Assurance Protocols", "id": 42, "limit": "33", "reviewer": "<PERSON><PERSON>", "status": "Done", "target": "30", "type": "Technical content"}, {"header": "Stakeholder Analysis", "id": 43, "limit": "14", "reviewer": "<PERSON>", "status": "In Process", "target": "11", "type": "Research"}, {"header": "Environmental Impact Assessment", "id": 44, "limit": "27", "reviewer": "Assign reviewer", "status": "Done", "target": "24", "type": "Research"}, {"header": "Intellectual Property Rights", "id": 45, "limit": "20", "reviewer": "<PERSON>", "status": "In Process", "target": "17", "type": "Legal"}, {"header": "Customer Support Framework", "id": 46, "limit": "25", "reviewer": "<PERSON><PERSON>", "status": "Done", "target": "22", "type": "Narrative"}, {"header": "Version Control Strategy", "id": 47, "limit": "18", "reviewer": "Assign reviewer", "status": "In Process", "target": "15", "type": "Technical content"}, {"header": "Continuous Integration Pipeline", "id": 48, "limit": "29", "reviewer": "<PERSON>", "status": "Done", "target": "26", "type": "Technical content"}, {"header": "Regulatory Compliance", "id": 49, "limit": "16", "reviewer": "Assign reviewer", "status": "In Process", "target": "13", "type": "Legal"}, {"header": "User Authentication System", "id": 50, "limit": "31", "reviewer": "<PERSON>", "status": "Done", "target": "28", "type": "Technical content"}, {"header": "Data Analytics Framework", "id": 51, "limit": "24", "reviewer": "<PERSON><PERSON>", "status": "In Process", "target": "21", "type": "Technical content"}, {"header": "Cloud Infrastructure", "id": 52, "limit": "19", "reviewer": "Assign reviewer", "status": "Done", "target": "16", "type": "Technical content"}, {"header": "Network Security Measures", "id": 53, "limit": "32", "reviewer": "<PERSON>", "status": "In Process", "target": "29", "type": "Technical content"}, {"header": "Project Timeline", "id": 54, "limit": "17", "reviewer": "<PERSON>", "status": "Done", "target": "14", "type": "Planning"}, {"header": "Resource Allocation", "id": 55, "limit": "30", "reviewer": "Assign reviewer", "status": "In Process", "target": "27", "type": "Planning"}, {"header": "Team Structure and Roles", "id": 56, "limit": "23", "reviewer": "<PERSON><PERSON>", "status": "Done", "target": "20", "type": "Planning"}, {"header": "Communication Protocols", "id": 57, "limit": "18", "reviewer": "Assign reviewer", "status": "In Process", "target": "15", "type": "Planning"}, {"header": "Success Metrics", "id": 58, "limit": "33", "reviewer": "<PERSON>", "status": "Done", "target": "30", "type": "Planning"}, {"header": "Internationalization Support", "id": 59, "limit": "26", "reviewer": "<PERSON><PERSON>", "status": "In Process", "target": "23", "type": "Technical content"}, {"header": "Backup and Recovery Procedures", "id": 60, "limit": "21", "reviewer": "Assign reviewer", "status": "Done", "target": "18", "type": "Technical content"}, {"header": "Monitoring and Alerting System", "id": 61, "limit": "28", "reviewer": "Daniel <PERSON>", "status": "In Process", "target": "25", "type": "Technical content"}, {"header": "Code Review Guidelines", "id": 62, "limit": "15", "reviewer": "<PERSON>", "status": "Done", "target": "12", "type": "Technical content"}, {"header": "Documentation Standards", "id": 63, "limit": "30", "reviewer": "<PERSON><PERSON>", "status": "In Process", "target": "27", "type": "Technical content"}, {"header": "Release Management Process", "id": 64, "limit": "25", "reviewer": "Assign reviewer", "status": "Done", "target": "22", "type": "Planning"}, {"header": "Feature Prioritization Matrix", "id": 65, "limit": "22", "reviewer": "<PERSON>", "status": "In Process", "target": "19", "type": "Planning"}, {"header": "Technical Debt Assessment", "id": 66, "limit": "27", "reviewer": "<PERSON>", "status": "Done", "target": "24", "type": "Technical content"}, {"header": "Capacity Planning", "id": 67, "limit": "24", "reviewer": "<PERSON><PERSON>", "status": "In Process", "target": "21", "type": "Planning"}, {"header": "Service Level Agreements", "id": 68, "limit": "29", "reviewer": "Assign reviewer", "status": "Done", "target": "26", "type": "Legal"}]