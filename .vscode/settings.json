{"[css]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "cSpell.words": ["sonner", "tabler", "turbopack", "vaul", "weibo"], "editor.codeActionsOnSave": {"source.action.useSortedKeys.biome": "explicit", "source.fixAll.biome": "explicit", "source.organizeImports.biome": "explicit"}, "editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true}