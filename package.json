{"dependencies": {"@aws-sdk/client-s3": "^3.848.0", "@aws-sdk/s3-request-presigner": "^3.848.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@icons-pack/react-simple-icons": "^13.5.0", "@prisma/client": "^6.12.0", "@prisma/extension-accelerate": "^2.0.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "better-auth": "^1.3.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gsap": "^3.13.0", "lucide-react": "^0.525.0", "next": "15.4.3", "next-themes": "^0.4.6", "postcss": "^8.5.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-image-crop": "^11.0.10", "recharts": "^3.1.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^4.0.8"}, "devDependencies": {"@biomejs/biome": "2.1.2", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query-devtools": "^5.83.0", "@types/node": "^24.1.0", "@types/react": "^19", "@types/react-dom": "^19", "prisma": "^6.12.0", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "tw-animate-css": "^1.3.5", "typescript": "^5"}, "name": "library", "prisma": {"schema": "./prisma"}, "private": true, "scripts": {"build": "prisma generate --no-engine && next build", "db:generate": "prisma generate", "db:push": "prisma db push", "dev": "prisma generate && next dev --turbopack", "lint": "next lint", "start": "next start"}, "version": "0.1.0"}